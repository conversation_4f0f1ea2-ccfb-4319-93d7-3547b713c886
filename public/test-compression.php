<?php
/**
 * Test script to verify compression is working
 * Access this file via browser to check compression headers
 */

// Set content type
header('Content-Type: text/html; charset=utf-8');

// Check if compression is enabled
$compression_enabled = false;
$compression_type = 'None';

if (isset($_SERVER['HTTP_ACCEPT_ENCODING'])) {
    if (strpos($_SERVER['HTTP_ACCEPT_ENCODING'], 'gzip') !== false) {
        $compression_enabled = true;
        $compression_type = 'gzip';
    } elseif (strpos($_SERVER['HTTP_ACCEPT_ENCODING'], 'deflate') !== false) {
        $compression_enabled = true;
        $compression_type = 'deflate';
    }
}

// Get server info
$server_software = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
$php_version = phpversion();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compression Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .test-content {
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗜️ Compression Test Results</h1>
        
        <?php if ($compression_enabled): ?>
            <div class="status success">
                <strong>✅ Compression is ENABLED</strong><br>
                Your browser supports <?php echo $compression_type; ?> compression.
            </div>
        <?php else: ?>
            <div class="status warning">
                <strong>⚠️ Compression support unclear</strong><br>
                Your browser may not support compression or headers are not being sent properly.
            </div>
        <?php endif; ?>

        <h2>Server Information</h2>
        <table>
            <tr>
                <th>Server Software</th>
                <td><?php echo htmlspecialchars($server_software); ?></td>
            </tr>
            <tr>
                <th>PHP Version</th>
                <td><?php echo $php_version; ?></td>
            </tr>
            <tr>
                <th>Accept-Encoding Header</th>
                <td><?php echo htmlspecialchars($_SERVER['HTTP_ACCEPT_ENCODING'] ?? 'Not set'); ?></td>
            </tr>
            <tr>
                <th>Content-Encoding</th>
                <td><?php echo htmlspecialchars($_SERVER['HTTP_CONTENT_ENCODING'] ?? 'Not set'); ?></td>
            </tr>
        </table>

        <h2>How to Test Uploads Compression</h2>
        <div class="info">
            <p><strong>To test compression for files in /public/uploads:</strong></p>
            <ol>
                <li>Upload some files to your uploads directory</li>
                <li>Use browser developer tools (F12) → Network tab</li>
                <li>Access a file directly: <code><?php echo $_SERVER['HTTP_HOST']; ?>/public/uploads/your-file.jpg</code></li>
                <li>Check the Response Headers for <code>Content-Encoding: gzip</code> or <code>Content-Encoding: deflate</code></li>
            </ol>
        </div>

        <h2>Command Line Test</h2>
        <div class="info">
            <p><strong>Test compression via curl:</strong></p>
            <pre><code>curl -H "Accept-Encoding: gzip" -I http://<?php echo $_SERVER['HTTP_HOST']; ?>/public/uploads/your-file.jpg</code></pre>
            <p>Look for <code>Content-Encoding: gzip</code> in the response headers.</p>
        </div>

        <div class="test-content">
            <h3>Sample Content for Compression Testing</h3>
            <p>This is a large block of text that should be compressed when served. <?php echo str_repeat('This text is repeated to make the content larger and more suitable for compression testing. ', 50); ?></p>
        </div>

        <div class="status info">
            <strong>📝 Note:</strong> The compression configuration has been applied to your <code>/public/uploads/</code> directory. 
            Files served from this directory should now be compressed automatically by Apache when the client supports it.
        </div>
    </div>
</body>
</html>
